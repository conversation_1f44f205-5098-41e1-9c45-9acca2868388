'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { auth, AuthUser } from '@/lib/auth'

interface AuthContextType {
  user: AuthUser | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<{ error: any }>
  signUp: (email: string, password: string, fullName?: string) => Promise<{ error: any }>
  signOut: () => Promise<void>
  updateProfile: (updates: { full_name?: string; avatar_url?: string }) => Promise<{ error: any }>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    let mounted = true

    // Timeout to prevent infinite loading
    const loadingTimeout = setTimeout(() => {
      if (mounted) {
        console.warn('Auth loading timeout - forcing loading to false')
        setLoading(false)
      }
    }, 10000) // 10 second timeout

    // Get initial user with error handling
    const initializeAuth = async () => {
      try {
        const user = await auth.getCurrentUser()
        if (mounted) {
          setUser(user)
          setLoading(false)
          clearTimeout(loadingTimeout)
        }
      } catch (error) {
        console.error('Error getting current user:', error)
        if (mounted) {
          setUser(null)
          setLoading(false)
          clearTimeout(loadingTimeout)
        }
      }
    }

    initializeAuth()

    // Listen for auth changes with error handling
    const { data: { subscription } } = auth.onAuthStateChange(async (user) => {
      try {
        if (mounted) {
          setUser(user)
          setLoading(false)
          clearTimeout(loadingTimeout)
        }
      } catch (error) {
        console.error('Error in auth state change:', error)
        if (mounted) {
          setUser(null)
          setLoading(false)
          clearTimeout(loadingTimeout)
        }
      }
    })

    return () => {
      mounted = false
      clearTimeout(loadingTimeout)
      subscription.unsubscribe()
    }
  }, [])

  const signIn = async (email: string, password: string) => {
    setLoading(true)
    const { error } = await auth.signIn(email, password)
    setLoading(false)
    return { error }
  }

  const signUp = async (email: string, password: string, fullName?: string) => {
    setLoading(true)
    const { error } = await auth.signUp(email, password, fullName)
    setLoading(false)
    return { error }
  }

  const signOut = async () => {
    setLoading(true)
    await auth.signOut()
    setUser(null)
    setLoading(false)
  }

  const updateProfile = async (updates: { full_name?: string; avatar_url?: string }) => {
    const { error } = await auth.updateProfile(updates)
    if (!error && user) {
      // Filter out undefined values and convert them to null
      const profileUpdates: { full_name: string | null; avatar_url: string | null } = {
        full_name: updates.full_name !== undefined ? updates.full_name : user.profile?.full_name || null,
        avatar_url: updates.avatar_url !== undefined ? updates.avatar_url : user.profile?.avatar_url || null
      }

      setUser({
        ...user,
        profile: profileUpdates
      })
    }
    return { error }
  }

  const value = {
    user,
    loading,
    signIn,
    signUp,
    signOut,
    updateProfile,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
