'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/auth/auth-provider'
import { petStorage, PetWithOwnership } from '@/lib/storage'
import { PetProfileEditor } from '@/components/pet-profile-editor'
import { PetAvatar } from '@/components/pet-avatar'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import { Plus, Paw<PERSON><PERSON>t, <PERSON>hare2, <PERSON><PERSON><PERSON>, Trash2, Users, Edit } from 'lucide-react'

interface PetManagerProps {
  selectedPetId: string | null
  onPetSelect: (petId: string | null) => void
  onPetUpdate: () => void
}

export function PetManager({ selectedPetId, onPetSelect, onPetUpdate }: PetManagerProps) {
  const { user } = useAuth()
  const { toast } = useToast()
  const [pets, setPets] = useState<PetWithOwnership[]>([])
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isShareDialogOpen, setIsShareDialogOpen] = useState(false)
  const [isManageShareDialogOpen, setIsManageShareDialogOpen] = useState(false)
  const [selectedPetForSharing, setSelectedPetForSharing] = useState<string | null>(null)
  const [selectedPetForManaging, setSelectedPetForManaging] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  
  const [newPet, setNewPet] = useState({
    name: '',
    nickname: '',
    species: '',
    breed: '',
    date_of_birth: '',
    weight: '',
    color: '',
    notes: ''
  })

  const [shareData, setShareData] = useState({
    email: '',
    role: 'caretaker' as 'owner' | 'caretaker'
  })

  useEffect(() => {
    loadPets()
  }, [])

  const loadPets = async () => {
    if (!user) return
    
    setLoading(true)
    try {
      const petsData = await petStorage.getPets()
      setPets(petsData)
      
      // If no pet is selected and we have pets, select the first one
      if (!selectedPetId && petsData.length > 0) {
        onPetSelect(petsData[0].id)
      }
    } catch (error) {
      console.error('Error loading pets:', error)
      toast({
        title: "Error",
        description: "Failed to load pets",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCreatePet = async () => {
    if (!newPet.name || !newPet.species) {
      toast({
        title: "Error",
        description: "Please fill in the required fields (name and species)",
        variant: "destructive"
      })
      return
    }

    setLoading(true)
    try {
      const pet = await petStorage.createPet({
        name: newPet.name,
        nickname: newPet.nickname || null,
        species: newPet.species,
        breed: newPet.breed || null,
        date_of_birth: newPet.date_of_birth || null,
        weight: newPet.weight || null,
        color: newPet.color || null,
        notes: newPet.notes || null
      })

      if (pet) {
        await loadPets()
        onPetSelect(pet.id)
        onPetUpdate()
        setNewPet({
          name: '',
          nickname: '',
          species: '',
          breed: '',
          date_of_birth: '',
          weight: '',
          color: '',
          notes: ''
        })
        setIsCreateDialogOpen(false)
        toast({
          title: "Success",
          description: `${pet.name} has been added to your pets`
        })
      }
    } catch (error) {
      console.error('Error creating pet:', error)
      toast({
        title: "Error",
        description: "Failed to create pet",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSharePet = async () => {
    if (!selectedPetForSharing || !shareData.email) {
      toast({
        title: "Error",
        description: "Please fill in all fields",
        variant: "destructive"
      })
      return
    }

    setLoading(true)
    try {
      const success = await petStorage.sharePet(selectedPetForSharing, shareData.email, shareData.role)
      
      if (success) {
        await loadPets()
        setShareData({ email: '', role: 'caretaker' })
        setIsShareDialogOpen(false)
        setSelectedPetForSharing(null)
        toast({
          title: "Success",
          description: "Pet has been shared successfully"
        })
      } else {
        toast({
          title: "Error",
          description: "Failed to share pet. User may not exist or already has access.",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('Error sharing pet:', error)
      toast({
        title: "Error",
        description: "Failed to share pet",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleRemovePetAccess = async (petId: string, userId: string, userEmail: string) => {
    setLoading(true)
    try {
      const success = await petStorage.removePetAccess(petId, userId)

      if (success) {
        await loadPets()
        onPetUpdate()

        toast({
          title: "Access removed",
          description: `Removed access for ${userEmail}`,
        })
      }
    } catch (error) {
      console.error('Error removing pet access:', error)
      toast({
        title: "Error",
        description: "Failed to remove access",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleDeletePet = async (petId: string) => {
    if (!confirm('Are you sure you want to delete this pet? This action cannot be undone.')) {
      return
    }

    setLoading(true)
    try {
      const success = await petStorage.deletePet(petId)
      
      if (success) {
        await loadPets()
        if (selectedPetId === petId) {
          onPetSelect(null)
        }
        onPetUpdate()
        toast({
          title: "Success",
          description: "Pet has been deleted"
        })
      }
    } catch (error) {
      console.error('Error deleting pet:', error)
      toast({
        title: "Error",
        description: "Failed to delete pet",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const selectedPet = pets.find(pet => pet.id === selectedPetId)

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">My Pets</h2>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Pet
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Add New Pet</DialogTitle>
              <DialogDescription>
                Add a new pet to your collection
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Name *</Label>
                  <Input
                    id="name"
                    value={newPet.name}
                    onChange={(e) => setNewPet({ ...newPet, name: e.target.value })}
                    placeholder="Pet's name"
                  />
                </div>
                <div>
                  <Label htmlFor="species">Species *</Label>
                  <Select value={newPet.species} onValueChange={(value) => setNewPet({ ...newPet, species: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select species" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="cat">Cat</SelectItem>
                      <SelectItem value="dog">Dog</SelectItem>
                      <SelectItem value="bird">Bird</SelectItem>
                      <SelectItem value="rabbit">Rabbit</SelectItem>
                      <SelectItem value="hamster">Hamster</SelectItem>
                      <SelectItem value="fish">Fish</SelectItem>
                      <SelectItem value="reptile">Reptile</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="nickname">Nickname</Label>
                <Input
                  id="nickname"
                  value={newPet.nickname}
                  onChange={(e) => setNewPet({ ...newPet, nickname: e.target.value })}
                  placeholder="Pet's nickname (optional)"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="breed">Breed</Label>
                  <Input
                    id="breed"
                    value={newPet.breed}
                    onChange={(e) => setNewPet({ ...newPet, breed: e.target.value })}
                    placeholder="Breed (optional)"
                  />
                </div>
                <div>
                  <Label htmlFor="dob">Date of Birth</Label>
                  <Input
                    id="dob"
                    type="date"
                    value={newPet.date_of_birth}
                    onChange={(e) => setNewPet({ ...newPet, date_of_birth: e.target.value })}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="weight">Weight</Label>
                  <Input
                    id="weight"
                    value={newPet.weight}
                    onChange={(e) => setNewPet({ ...newPet, weight: e.target.value })}
                    placeholder="e.g., 5.2 kg"
                  />
                </div>
                <div>
                  <Label htmlFor="color">Color</Label>
                  <Input
                    id="color"
                    value={newPet.color}
                    onChange={(e) => setNewPet({ ...newPet, color: e.target.value })}
                    placeholder="Pet's color"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  value={newPet.notes}
                  onChange={(e) => setNewPet({ ...newPet, notes: e.target.value })}
                  placeholder="Any additional notes about your pet"
                  rows={3}
                />
              </div>

              <div className="flex space-x-2 pt-4">
                <Button onClick={handleCreatePet} className="flex-1" disabled={loading}>
                  {loading ? 'Creating...' : 'Create Pet'}
                </Button>
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {pets.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-8">
            <PawPrint className="h-12 w-12 text-gray-400 mb-4" />
            <p className="text-gray-500 text-center">
              No pets yet. Add your first pet to get started!
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {pets.map((pet) => (
            <Card 
              key={pet.id} 
              className={`cursor-pointer transition-all ${
                selectedPetId === pet.id ? 'ring-2 ring-blue-500' : 'hover:shadow-md'
              }`}
              onClick={() => onPetSelect(pet.id)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center space-x-3">
                  <PetAvatar
                    imageUrl={pet.image_url}
                    petName={pet.name}
                    size="lg"
                  />
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{pet.name}</CardTitle>
                      <div className="flex space-x-1">
                        {pet.is_creator && (
                          <Badge variant="default">Owner</Badge>
                        )}
                        {!pet.is_creator && (
                          <Badge variant="secondary">Shared</Badge>
                        )}
                      </div>
                    </div>
                    <CardDescription>
                      {pet.species} {pet.breed && `• ${pet.breed}`}
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-600">
                    {pet.pet_owners.length > 1 && (
                      <div className="flex items-center">
                        <Users className="h-4 w-4 mr-1" />
                        {pet.pet_owners.length} caretakers
                      </div>
                    )}
                  </div>
                  <div className="flex space-x-1">
                    {pet.is_creator && (
                      <>
                        <PetProfileEditor
                          pet={pet}
                          onUpdate={() => {
                            loadPets()
                            onPetUpdate()
                          }}
                          trigger={
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={(e) => e.stopPropagation()}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                          }
                        />
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation()
                            setSelectedPetForSharing(pet.id)
                            setIsShareDialogOpen(true)
                          }}
                        >
                          <Share2 className="h-4 w-4" />
                        </Button>
                        {pet.pet_owners.length > 1 && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={(e) => {
                              e.stopPropagation()
                              setSelectedPetForManaging(pet.id)
                              setIsManageShareDialogOpen(true)
                            }}
                          >
                            <Settings className="h-4 w-4" />
                          </Button>
                        )}
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleDeletePet(pet.id)
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Share Pet Dialog */}
      <Dialog open={isShareDialogOpen} onOpenChange={setIsShareDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Share Pet</DialogTitle>
            <DialogDescription>
              Share access to your pet with another user
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="share-email">User Email</Label>
              <Input
                id="share-email"
                type="email"
                value={shareData.email}
                onChange={(e) => setShareData({ ...shareData, email: e.target.value })}
                placeholder="Enter user's email address"
              />
            </div>
            
            <div>
              <Label htmlFor="share-role">Role</Label>
              <Select value={shareData.role} onValueChange={(value: 'owner' | 'caretaker') => setShareData({ ...shareData, role: value })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="caretaker">Caretaker (can view and add records)</SelectItem>
                  <SelectItem value="owner">Owner (can manage pet and sharing)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex space-x-2 pt-4">
              <Button onClick={handleSharePet} className="flex-1" disabled={loading}>
                {loading ? 'Sharing...' : 'Share Pet'}
              </Button>
              <Button variant="outline" onClick={() => setIsShareDialogOpen(false)}>
                Cancel
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Manage Sharing Dialog */}
      <Dialog open={isManageShareDialogOpen} onOpenChange={setIsManageShareDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Manage Pet Sharing</DialogTitle>
            <DialogDescription>
              View and manage who has access to this pet
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {selectedPetForManaging && (() => {
              const pet = pets.find(p => p.id === selectedPetForManaging)
              if (!pet) return null

              return (
                <div className="space-y-3">
                  <h4 className="font-medium">Current Access</h4>
                  {pet.pet_owners.map((owner) => (
                    <div key={owner.user_id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">{owner.profiles?.email || 'Unknown User'}</p>
                        <Badge variant={owner.role === 'owner' ? 'default' : 'secondary'}>
                          {owner.role}
                        </Badge>
                      </div>
                      {owner.user_id !== user?.id && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleRemovePetAccess(pet.id, owner.user_id, owner.profiles?.email || 'Unknown User')}
                          disabled={loading}
                        >
                          Remove Access
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              )
            })()}

            <div className="flex justify-end pt-4">
              <Button variant="outline" onClick={() => setIsManageShareDialogOpen(false)}>
                Close
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
