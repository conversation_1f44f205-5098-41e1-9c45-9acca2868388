import { supabase } from './supabase'
import { Tables, Inserts, Updates } from './supabase'

// Type definitions for our data models
export interface Pet extends Tables<'pets'> {}
export interface PetOwner extends Tables<'pet_owners'> {}
export interface FeedingLog extends Tables<'feeding_logs'> {}
export interface LitterLog extends Tables<'litter_logs'> {}
export interface Vaccination extends Tables<'vaccinations'> {}
export interface Note extends Tables<'notes'> {}

// Extended PetOwner with profile information
export interface PetOwnerWithProfile extends PetOwner {
  profiles?: {
    email: string
    full_name: string | null
  }
}

export interface PetWithOwnership extends Pet {
  pet_owners: PetOwnerWithProfile[]
  is_owner: boolean
  is_creator: boolean
}

// Data access layer for Supabase operations
class SupabaseDataManager {
  // Helper to get current user ID
  private async getCurrentUserId(): Promise<string | null> {
    const { data: { user } } = await supabase.auth.getUser()
    return user?.id || null
  }

  // Pet operations
  async getPets(): Promise<PetWithOwnership[]> {
    const userId = await this.getCurrentUserId()
    if (!userId) return []

    // RLS policies will automatically filter pets the user has access to
    const { data, error } = await supabase
      .from('pets')
      .select(`
        *,
        pet_owners (
          *,
          profiles (
            email,
            full_name
          )
        )
      `)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching pets:', error)
      return []
    }

    return (data || []).map(pet => ({
      ...pet,
      is_owner: pet.pet_owners.some((po: PetOwnerWithProfile) => po.user_id === userId && po.role === 'owner'),
      is_creator: pet.created_by === userId
    }))
  }



  async createPet(petData: Omit<Inserts<'pets'>, 'created_by'>): Promise<Pet | null> {
    const userId = await this.getCurrentUserId()
    if (!userId) return null

    const { data, error } = await supabase
      .from('pets')
      .insert({
        ...petData,
        created_by: userId
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating pet:', error)
      return null
    }

    // The trigger will automatically create the owner relationship
    return data
  }

  async updatePet(petId: string, updates: Updates<'pets'>): Promise<Pet | null> {
    const { data, error } = await supabase
      .from('pets')
      .update(updates)
      .eq('id', petId)
      .select()
      .single()

    if (error) {
      console.error('Error updating pet:', error)
      return null
    }

    return data
  }

  async deletePet(petId: string): Promise<boolean> {
    const { error } = await supabase
      .from('pets')
      .delete()
      .eq('id', petId)

    if (error) {
      console.error('Error deleting pet:', error)
      return false
    }

    return true
  }

  // Pet sharing operations
  async sharePet(petId: string, userEmail: string, role: 'owner' | 'caretaker' = 'caretaker'): Promise<boolean> {
    // First, find the user by email
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id')
      .eq('email', userEmail)
      .single()

    if (profileError || !profile) {
      console.error('User not found:', profileError)
      return false
    }

    const { error } = await supabase
      .from('pet_owners')
      .insert({
        pet_id: petId,
        user_id: profile.id,
        role
      })

    if (error) {
      console.error('Error sharing pet:', error)
      return false
    }

    return true
  }

  async removePetAccess(petId: string, userId: string): Promise<boolean> {
    const { error } = await supabase
      .from('pet_owners')
      .delete()
      .eq('pet_id', petId)
      .eq('user_id', userId)

    if (error) {
      console.error('Error removing pet access:', error)
      return false
    }

    return true
  }

  // Feeding log operations
  async getFeedingLogs(petId?: string): Promise<FeedingLog[]> {
    const userId = await this.getCurrentUserId()
    if (!userId) return []

    let query = supabase
      .from('feeding_logs')
      .select('*')
      .order('timestamp', { ascending: false })

    if (petId) {
      query = query.eq('pet_id', petId)
    }

    const { data, error } = await query

    if (error) {
      console.error('Error fetching feeding logs:', error)
      return []
    }

    return data || []
  }

  async addFeedingLog(log: Omit<Inserts<'feeding_logs'>, 'user_id'>): Promise<FeedingLog | null> {
    const userId = await this.getCurrentUserId()
    if (!userId) return null

    const { data, error } = await supabase
      .from('feeding_logs')
      .insert({
        ...log,
        user_id: userId
      })
      .select()
      .single()

    if (error) {
      console.error('Error adding feeding log:', error)
      return null
    }

    return data
  }

  async removeFeedingLog(id: string): Promise<boolean> {
    const { error } = await supabase
      .from('feeding_logs')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('Error removing feeding log:', error)
      return false
    }

    return true
  }

  // Litter log operations
  async getLitterLogs(petId?: string): Promise<LitterLog[]> {
    const userId = await this.getCurrentUserId()
    if (!userId) return []

    let query = supabase
      .from('litter_logs')
      .select('*')
      .order('timestamp', { ascending: false })

    if (petId) {
      query = query.eq('pet_id', petId)
    }

    const { data, error } = await query

    if (error) {
      console.error('Error fetching litter logs:', error)
      return []
    }

    return data || []
  }

  async addLitterLog(log: Omit<Inserts<'litter_logs'>, 'user_id'>): Promise<LitterLog | null> {
    const userId = await this.getCurrentUserId()
    if (!userId) return null

    const { data, error } = await supabase
      .from('litter_logs')
      .insert({
        ...log,
        user_id: userId
      })
      .select()
      .single()

    if (error) {
      console.error('Error adding litter log:', error)
      return null
    }

    return data
  }

  async removeLitterLog(id: string): Promise<boolean> {
    const { error } = await supabase
      .from('litter_logs')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('Error removing litter log:', error)
      return false
    }

    return true
  }

  // Vaccination operations
  async getVaccinations(petId?: string): Promise<Vaccination[]> {
    const userId = await this.getCurrentUserId()
    if (!userId) return []

    let query = supabase
      .from('vaccinations')
      .select('*')
      .order('date', { ascending: false })

    if (petId) {
      query = query.eq('pet_id', petId)
    }

    const { data, error } = await query

    if (error) {
      console.error('Error fetching vaccinations:', error)
      return []
    }

    return data || []
  }

  async addVaccination(vaccination: Omit<Inserts<'vaccinations'>, 'user_id'>): Promise<Vaccination | null> {
    const userId = await this.getCurrentUserId()
    if (!userId) return null

    const { data, error } = await supabase
      .from('vaccinations')
      .insert({
        ...vaccination,
        user_id: userId
      })
      .select()
      .single()

    if (error) {
      console.error('Error adding vaccination:', error)
      return null
    }

    return data
  }

  async removeVaccination(id: string): Promise<boolean> {
    const { error } = await supabase
      .from('vaccinations')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('Error removing vaccination:', error)
      return false
    }

    return true
  }

  // Notes operations
  async getNotes(petId?: string): Promise<Note[]> {
    const userId = await this.getCurrentUserId()
    if (!userId) return []

    let query = supabase
      .from('notes')
      .select('*')
      .order('timestamp', { ascending: false })

    if (petId) {
      query = query.eq('pet_id', petId)
    }

    const { data, error } = await query

    if (error) {
      console.error('Error fetching notes:', error)
      return []
    }

    return data || []
  }

  async addNote(note: Omit<Inserts<'notes'>, 'user_id'>): Promise<Note | null> {
    const userId = await this.getCurrentUserId()
    if (!userId) return null

    const { data, error } = await supabase
      .from('notes')
      .insert({
        ...note,
        user_id: userId
      })
      .select()
      .single()

    if (error) {
      console.error('Error adding note:', error)
      return null
    }

    return data
  }

  async removeNote(id: string): Promise<boolean> {
    const { error } = await supabase
      .from('notes')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('Error removing note:', error)
      return false
    }

    return true
  }
}

// Create singleton instance
export const storage = new SupabaseDataManager();

// New Supabase-based storage interface
export const petStorage = {
  // Pet operations
  getPets: () => storage.getPets(),
  createPet: (petData: Omit<Inserts<'pets'>, 'created_by'>) => storage.createPet(petData),
  updatePet: (petId: string, updates: Updates<'pets'>) => storage.updatePet(petId, updates),
  deletePet: (petId: string) => storage.deletePet(petId),
  sharePet: (petId: string, userEmail: string, role?: 'owner' | 'caretaker') => storage.sharePet(petId, userEmail, role),
  removePetAccess: (petId: string, userId: string) => storage.removePetAccess(petId, userId),

  // Feeding logs
  getFeedingLogs: (petId?: string) => storage.getFeedingLogs(petId),
  addFeedingLog: (log: Omit<Inserts<'feeding_logs'>, 'user_id'>) => storage.addFeedingLog(log),
  removeFeedingLog: (id: string) => storage.removeFeedingLog(id),

  // Litter logs
  getLitterLogs: (petId?: string) => storage.getLitterLogs(petId),
  addLitterLog: (log: Omit<Inserts<'litter_logs'>, 'user_id'>) => storage.addLitterLog(log),
  removeLitterLog: (id: string) => storage.removeLitterLog(id),

  // Vaccinations
  getVaccinations: (petId?: string) => storage.getVaccinations(petId),
  addVaccination: (vaccination: Omit<Inserts<'vaccinations'>, 'user_id'>) => storage.addVaccination(vaccination),
  removeVaccination: (id: string) => storage.removeVaccination(id),

  // Notes
  getNotes: (petId?: string) => storage.getNotes(petId),
  addNote: (note: Omit<Inserts<'notes'>, 'user_id'>) => storage.addNote(note),
  removeNote: (id: string) => storage.removeNote(id),
};

// React hook for listening to storage changes
export function useStorageListener(callback: (key: string) => void) {
  if (typeof window !== 'undefined') {
    const handleStorageChange = (event: CustomEvent) => {
      callback(event.detail.key);
    };

    window.addEventListener('pet-data-updated', handleStorageChange as EventListener);
    
    return () => {
      window.removeEventListener('pet-data-updated', handleStorageChange as EventListener);
    };
  }
  return () => {};
}